import { PaymentIntent } from '@stripe/stripe-js';
import { useState } from 'react';
import { Loader2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from '@/lib/i18n/routing';
import StripePaymentHandler from './StripePaymentHandler';

export function PaymentStatus() {
  const t = useTranslations('checkout');
  const router = useRouter();
  const [message, setMessage] = useState<string | null>(null);
  const [loading] = useState<boolean>(true);

  const handlePaymentSuccess = () => {
    router.push('/results?init=1');
  };

  const handlePaymentProcessing = () => {
    setMessage(t('payment_processing'));
  };

  const handlePaymentFailed = () => {
    setMessage(t('payment_failed'));
  };

  const handlePaymentError = () => {
    setMessage(t('payment_error'));
  };

  return (
    <>
      <StripePaymentHandler
        onSuccess={handlePaymentSuccess}
        onProcessing={handlePaymentProcessing}
        onFailed={handlePaymentFailed}
        onError={handlePaymentError}
        autoTrackEvents={true}
      />

      <div className="flex items-center">
        {message ? (
          <div>{message}</div>
        ) : (
          <h3>
            {t('checking_payment_status')}
            {loading ? <Loader2 className="h-10 w-10 m-auto animate-spin mt-5 text-primary" /> : ''}
          </h3>
        )}
      </div>
    </>
  );
}
