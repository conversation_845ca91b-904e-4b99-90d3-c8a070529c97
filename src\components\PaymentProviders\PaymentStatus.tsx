import { PaymentIntent } from '@stripe/stripe-js';
import { useState } from 'react';
import { Loader2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from '@/lib/i18n/routing';
import StripePaymentHandler from './StripePaymentHandler';

export function PaymentStatus() {
  const t = useTranslations('checkout');
  const router = useRouter();
  const [message, setMessage] = useState<string | null>(null);
  const [loading] = useState<boolean>(true);

  /**
   * Handles a successful payment by logging details and redirecting to the results page.
   *
   * @param paymentIntent - The Stripe PaymentIntent object, if available.
   */
  const handlePaymentSuccess = (paymentIntent?: PaymentIntent): void => {
    console.log('Payment succeeded!', paymentIntent?.amount, paymentIntent?.payment_method, paymentIntent?.status);
    router.push('/results?init=1');
  };

  /**
   * Handles a payment that is still processing by logging details and showing a “processing” message.
   *
   * @param paymentIntent - The Stripe PaymentIntent object, if available.
   */
  const handlePaymentProcessing = (paymentIntent?: PaymentIntent): void => {
    console.log('Payment processing!', paymentIntent?.amount, paymentIntent?.payment_method, paymentIntent?.status);
    setMessage(t('payment_processing'));
  };

  /**
   * Handles a failed payment attempt by logging details and showing a “failed” message.
   *
   * @param paymentIntent - The Stripe PaymentIntent object, if available.
   */
  const handlePaymentFailed = (paymentIntent?: PaymentIntent): void => {
    console.log('Payment failed!', paymentIntent?.amount, paymentIntent?.payment_method, paymentIntent?.status);
    setMessage(t('payment_failed'));
  };

  /**
   * Handles any other payment errors by logging details and showing a generic error message.
   *
   * @param paymentIntent - The Stripe PaymentIntent object, if available.
   */
  const handlePaymentError = (paymentIntent?: PaymentIntent): void => {
    console.log('Payment error!', paymentIntent?.amount, paymentIntent?.payment_method, paymentIntent?.status);
    setMessage(t('payment_error'));
  };

  return (
    <>
      <StripePaymentHandler
        onSuccess={handlePaymentSuccess}
        onProcessing={handlePaymentProcessing}
        onFailed={handlePaymentFailed}
        onError={handlePaymentError}
        autoTrackEvents={true}
      />

      <div className="flex items-center">
        {message ? (
          <div>{message}</div>
        ) : (
          <h3>
            {t('checking_payment_status')}
            {loading ? <Loader2 className="h-10 w-10 m-auto animate-spin mt-5 text-primary" /> : ''}
          </h3>
        )}
      </div>
    </>
  );
}
