import { PaymentIntent } from '@stripe/stripe-js';
import { useState } from 'react';
import { Loader2 } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useRouter } from '@/lib/i18n/routing';
import StripePaymentHandler from './StripePaymentHandler';

export function PaymentStatus() {
  const t = useTranslations('checkout');
  const router = useRouter();
  const [message, setMessage] = useState<string | null>(null);
  const [loading] = useState<boolean>(true);

  /**
   * Redirects the user to the results page when payment succeeds.
   *
   * @param _paymentIntent - The Stripe PaymentIntent object, if available.
   */
  const handlePaymentSuccess = (_paymentIntent?: PaymentIntent): void => {
    router.push('/results?init=1');
  };

  /**
   * Shows a “processing” message while the payment is still in progress.
   *
   * @param paymentIntent - The Stripe PaymentIntent object, if available.
   */
  const handlePaymentProcessing = (_paymentIntent?: PaymentIntent): void => {
    setMessage(t('payment_processing'));
  };

  /**
   * Shows a “payment failed” message when the payment attempt fails.
   *
   * @param paymentIntent - The Stripe PaymentIntent object, if available.
   */
  const handlePaymentFailed = (_paymentIntent?: PaymentIntent): void => {
    setMessage(t('payment_failed'));
  };

  /**
   * Shows a generic error message if something goes wrong during payment.
   *
   * @param paymentIntent - The Stripe PaymentIntent object, if available.
   */
  const handlePaymentError = (_paymentIntent?: PaymentIntent): void => {
    setMessage(t('payment_error'));
  };

  return (
    <>
      <StripePaymentHandler
        onSuccess={handlePaymentSuccess}
        onProcessing={handlePaymentProcessing}
        onFailed={handlePaymentFailed}
        onError={handlePaymentError}
        autoTrackEvents={true}
      />

      <div className="flex items-center">
        {message ? (
          <div>{message}</div>
        ) : (
          <h3>
            {t('checking_payment_status')}
            {loading ? <Loader2 className="h-10 w-10 m-auto animate-spin mt-5 text-primary" /> : ''}
          </h3>
        )}
      </div>
    </>
  );
}
