'use client';

import CourseCard from '../CourseCard';
import VideoCourse from '../VideoCourse';
import { useContext, useEffect, useState, useMemo } from 'react';
import { useTranslations } from 'next-intl';
import { useRouter } from '@/lib/i18n/routing';
import EQ from '../EQ';
import { httpsCallable } from 'firebase/functions';
import { functions } from '@/utils/firebase';
import { Loader2 } from 'lucide-react';
import SessionContext from '@/store/SessionContext';
import { PostHogEventEnum } from '@/store/types';

export default function Academy() {
  const t = useTranslations('academy');
  const [content, setContent] = useState(1);
  const router = useRouter();
  const [isEQTestGiven, setIsEQTestGiven] = useState('/emotional-intelligence/test');
  const [isLQTestGiven, setIsLQTestGiven] = useState('/love-languages/test');
  const [loading, setLoading] = useState(true);
  const { updateEmotionalScores, updateResults } = useContext(SessionContext);
  const getTestHistory = httpsCallable(functions, 'getTestHistory');

  const redirect = page => {
    router.push(page);
  };

  useEffect(() => {
    getTestHistory()
      .then(res => {
        let latestEQItem = null;
        let latestLQItem = null;
        let latestEQDate = null;
        let latestLQDate = null;

        // Iterate over history to find the latest item for each type independently
        res.data.history.reverse().forEach(item => {
          const itemDate = new Date(item.date); // Convert the string date to Date object

          // Check if the item is emotional-intelligence and update the latestEQItem
          if (item.type === 'emotional-intelligence') {
            if (!latestEQDate || itemDate > latestEQDate) {
              latestEQDate = itemDate;
              latestEQItem = item;
            }
          }

          // Check if the item is love-languages and update the latestLQItem
          if (item.type === 'love-languages') {
            if (!latestLQDate || itemDate > latestLQDate) {
              latestLQDate = itemDate;
              latestLQItem = item;
            }
          }
        });

        // Handle the emotional-intelligence test results
        if (latestEQItem) {
          if (latestEQItem.emotionalScores && latestEQItem.type === 'emotional-intelligence') {
            updateEmotionalScores(latestEQItem.emotionalScores);
            setIsEQTestGiven('/emotional-intelligence/results?showResults=true');
          }
        }

        // Handle the love-languages test results
        if (latestLQItem) {
          if (latestLQItem.loveScore && latestLQItem.type === 'love-languages') {
            updateResults(latestLQItem.loveScore);
            setIsLQTestGiven('/love-languages/results?showResults=true');
          }
        }

        setLoading(false);
      })
      .catch(e => {
        console.error(e);
        setLoading(false);
      });

    // eslint-disable-next-line
  }, []);

  const AcademyOptions = useMemo(
    () => [
      {
        imgPath: '/images/videoCourse.svg',
        header: t('video_course_header'),
        text: t('video_course_text'),
        available: true,
        content: 2,
        imgWidth: 96,
        imgHeight: 80,
        event: PostHogEventEnum.GO_TO_VIDEO_COURSE_VIEW,
      },
      {
        imgPath: '/images/book.svg',
        header: t('iq_training_header'),
        text: t('iq_training_text'),
        available: true,
        content: 3,
        imgWidth: 86,
        page: '/training',
        imgHeight: 80,
        event: PostHogEventEnum.GO_TO_IQ_TRAINING_VIEW,
      },
      {
        imgPath: '/images/eq_test.svg',
        header: t('eq_test_header'),
        text: t('eq_test_text'),
        available: true,
        content: 4,
        imgWidth: 80,
        imgHeight: 80,
        page: isEQTestGiven,
        event: PostHogEventEnum.GO_TO_EQ_TEST_VIEW,
      },
      {
        imgPath: '/images/lq_test.svg',
        header: t('love_languages_test_header'),
        text: t('love_languages_test_text'),
        available: true,
        content: 4,
        imgWidth: 80,
        imgHeight: 80,
        page: isLQTestGiven,
        event: PostHogEventEnum.GO_TO_LOVE_LANGUAGES_TEST_VIEW,
      },
      {
        imgPath: '/images/toolkits.svg',
        header: t('toolkits_header'),
        text: t('toolkits_text'),
        available: true,
        content: 4,
        imgWidth: 80,
        imgHeight: 80,
        page: 'academy/toolkits',
      },
      {
        imgPath: '/images/advancedIQ.svg',
        header: t('advanced_iq_test_header'),
        text: t('advanced_iq_test_text'),
        available: false,
        content: 5,
        imgWidth: 80,
        imgHeight: 80,
        event: PostHogEventEnum.GO_TO_ADVANCED_IQ_TEST_VIEW,
      },
      {
        imgPath: '/images/SuperAdvancedIQ.svg',
        header: t('super_advanced_iq_test_header'),
        text: t('super_advanced_iq_test_text'),
        available: false,
        content: 6,
        imgWidth: 98,
        imgHeight: 80,
        event: PostHogEventEnum.GO_TO_SUPER_ADVANCED_IQ_TEST_VIEW,
      },
    ],
    [t, isEQTestGiven, isLQTestGiven]
  );

  return (
    <main>
      {loading ? (
        <div className="flex justify-center">
          <Loader2 className="h-4 w-4 animate-spin m-[5px] " />
        </div>
      ) : (
        <>
          {content == 4 && <EQ />}
          {content != 1 && content != 4 && (
            <div>
              <VideoCourse setContent={setContent} />
            </div>
          )}
          {content == 1 && (
            <div>
              <div className="md:flex justify-between pb-6 pt-10">
                <h1 className="text-[#191919] text-[52px] font-bold">{t('title')}</h1>
                {/* <div className="flex md:w-[440px] pt-5 justify-between">
              <div>
              <div className="flex justify-between w-[250px] md:w-[355px] p-2">
              <p className="text-[#8893AC] weight-[600]">
              {number} / {max}
              </p>
              <p className="text-[##191919] text-[18px] font-semibold">30 level</p>
              </div>
              <div className="w-100 h-1 bg-orange-100">
              <div className="h-1 bg-orange-300 w-[50%]"></div>
              </div>
              </div>
              <Image src="/images/star.svg" alt="Star" height={130} width={80} />
              </div> */}
              </div>
              <div className=" grid grid-cols-1 md:grid-cols-3 gap-8">
                {AcademyOptions.map(element => {
                  return (
                    <CourseCard
                      key={element.imgPath}
                      imgSource={element.imgPath}
                      imgWidth={element.imgWidth}
                      imgHeight={element.imgHeight}
                      header={element.header}
                      text={element.text}
                      available={element.available}
                      redirect={redirect}
                      page={element.page}
                      setContent={setContent}
                      content={element.content}
                      event={element.event}
                    />
                  );
                })}
              </div>
            </div>
          )}
        </>
      )}
    </main>
  );
}
