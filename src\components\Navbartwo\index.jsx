'use client'; // Enable client-side functionality

import { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { useTranslations } from 'next-intl';

export default function Navbartwo({ name, image, IQ }) {
  const t = useTranslations('checkout_v2.navbartwo');
  const [mounted, setMounted] = useState(false);
  const [container, setContainer] = useState(null);
  const [displayContent, setDisplayContent] = useState({ name, image, IQ });
  const [fade, setFade] = useState(false); // State for controlling fade

  useEffect(() => {
    // Ensure that `document.body` exists and create a container
    if (typeof document !== 'undefined' && document.body) {
      const div = document.createElement('div'); // Create a new div
      document.body.insertBefore(div, document.body.firstChild); // Insert div at the beginning of body
      setContainer(div); // Save the div as a container
      setMounted(true);
    }

    return () => {
      if (container) {
        document.body.removeChild(container); // Clean up by removing the div on unmount
      }
    };
  }, []);

  useEffect(() => {
    if (!mounted) return;

    // Fade out before updating content
    setFade(true); // Start fading out
    const timeout = setTimeout(() => {
      setDisplayContent({ name, image, IQ }); // Update content
      setFade(false); // Fade back in
    }, 500); // Match the timeout duration to the transition duration

    return () => clearTimeout(timeout); // Cleanup timeout on unmount
  }, [name, image, IQ, mounted]);

  if (!mounted || !container) return null;

  // This will append content to the created `div` at the start of the `body`
  return createPortal(
    <div className={`h-[40px] md:h-[52px]`}>
      <div className="border-b-2 fixed top-0 left-0 w-full bg-white z-[1000]">
        <div
          className={`flex items-center transition-opacity duration-500 ease-in-out justify-center ${
            fade ? 'opacity-0' : 'opacity-100'
          } h-10 md:h-[52px]`}>
          <p className="font-segoe font-normal text-[12px] leading-[18px] text-[#000000]">
            {t.rich('just_bought_message', { 
              name: displayContent.name,
              b: (chunks) => <b>{chunks}</b>
            })}
          </p>
          <img src={displayContent.image} className="mx-2 h-[19px] w-[26px]" alt={t('flag_alt')} />
          <p className="font-inter font-bold text-[14px] text-[10px] md:text-[16px] leading-[30px] text-primary"> IQ {displayContent.IQ}</p>
        </div>
      </div>
    </div>,
    container // Append it to the newly created container
  );
}