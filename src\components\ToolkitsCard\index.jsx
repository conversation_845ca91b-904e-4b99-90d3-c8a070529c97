import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

export default function ToolkitsCard({ imgSource, imgHeight, imgWidth, header, text, available, onClick }) {
  const t = useTranslations('toolkits');
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div className="flex flex-col gap-6 bg-white shadow-md p-4 md:w-[304px] w-full">
      <div className="flex justify-between">
        <Image
          src={`${imgSource}`}
          alt={t('alt.course_image')}
          width={imgWidth} // Let it scale dynamically
          height={imgHeight}
          quality={100}
        />
        <div
          className={`text-[16px] leading-[20px] py-[5px] px-[12px] rounded-[50px] h-fit ${
            available == true ? 'text-[#06C524] bg-[#06C5241A]' : 'text-[#DD3A16] bg-[#DD3A161A]'
          }`}>
          {available == true ? t('available') : t('locked')}
        </div>
      </div>
      <div className="flex flex-col gap-[6px]">
        <h2
          className={`max-w-[190px] ${available ? `text-[#191919]` : `text-[#8893AC]`} text-[22px] leading-[26px] font-semibold `}>
          {header}
        </h2>
        <p className="max-w-[190px] text-[#8893AC] text-[16px] leading-[20px] font-normal">{text}</p>
      </div>
      <button
        className={`flex flex-row gap-[6px] items-center justify-center py-2.5 w-full rounded-[10px] ${
          available
            ? 'border-solid border border-primary transition-all text-primary cursor-pointer hover:border-[#fff] hover:text-[#fff] hover:bg-primary'
            : 'border-solid border border-[#8893AC] text-[#8893AC] cursor-no-drop'
        }`}
        disabled={!available}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={onClick || (() => {})}>
        <span className="font-semibold text-[18px] leading-[24px] pt-[4px]">{t('button')}</span>
        {available ? (
          <img
            src={isHovered ? '/images/download_white.svg' : '/images/download.svg'}
            className="w-[16px] h-[16px]"
            alt="Download icon"
          />
        ) : (
          <img src="/images/lock.svg" className="w-[16px] h-[16px]" alt="Lock icon" />
        )}
      </button>
    </div>
  );
}
