import { useStripe } from '@stripe/react-stripe-js';
import { PaymentIntent } from '@stripe/stripe-js';
import { useContext, useEffect, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { useTrackGTMEvents } from '@/hooks/useTrackGTMEvents';
import { usePostHogEvents } from '@/hooks/usePostHogEvents';
import { useRouter } from '@/lib/i18n/routing';
import SessionContext from '@/store/SessionContext';
import { PaymentProvider } from '@/store/types';

interface StripePaymentHandlerProps {
  successRedirectUrl?: string;
  onSuccess?: (_: PaymentIntent | undefined) => void;
  onProcessing?: (_: PaymentIntent | undefined) => void;
  onFailed?: (_: PaymentIntent | undefined) => void;
  onError?: (_: PaymentIntent | undefined) => void;
  autoTrackEvents?: boolean;
}

/**
 * @param options - The options for the Stripe payment intent handler.
 * @returns The Stripe payment intent handler.
 */
function StripePaymentHandler({
  successRedirectUrl = '/results?init=1',
  onSuccess,
  onProcessing,
  onFailed,
  onError,
  autoTrackEvents = true,
}: StripePaymentHandlerProps) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const stripe = useStripe();

  const { checkoutId, checkoutVersion, prices } = useContext(SessionContext);
  const { trackPostHogPurchaseEvent } = usePostHogEvents();
  const { trackGTMPurchaseEvent } = useTrackGTMEvents();
  const hasFiredGTM = useRef(false);

  useEffect(() => {
    const payment_intent_client_secret = searchParams.get('payment_intent_client_secret');
    if (!stripe || !payment_intent_client_secret) return;

    stripe.retrievePaymentIntent(payment_intent_client_secret).then(({ paymentIntent }) => {
      console.log('Payment Intent Status: ', paymentIntent?.status);

      switch (paymentIntent?.status) {
        case 'succeeded':
          // Auto-track events if enabled
          if (autoTrackEvents) {
            // Track the purchase event in Google Tag Manager (GTM) analytics
            trackGTMPurchaseEvent(prices, checkoutId, hasFiredGTM);

            // Track the purchase event in PostHog analytics
            trackPostHogPurchaseEvent({
              paymentSystem: PaymentProvider.STRIPE,
              checkoutVersion,
              testType: 'iq',
              pageType: 'IQ',
              ...paymentIntent,
            });
          }

          // Custom success callback or default redirect
          if (onSuccess) {
            onSuccess(paymentIntent);
          } else {
            router.push(successRedirectUrl);
          }
          break;

        case 'processing':
          // Payment is still being processed
          if (onProcessing) {
            onProcessing(paymentIntent);
          }
          break;

        case 'requires_payment_method':
          // Payment failed, ask the user to try again
          if (onFailed) {
            onFailed(paymentIntent);
          }
          break;

        default:
          // Any other unexpected status
          if (onError) {
            onError(paymentIntent);
          }
          break;
      }
    });

    // eslint-disable-next-line
  }, [stripe, checkoutId, prices, router, searchParams, successRedirectUrl]);

  // This component doesn't render anything, it just handles the logic
  return null;
}

export default StripePaymentHandler;
