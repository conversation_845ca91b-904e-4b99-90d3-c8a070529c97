'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Dialog, DialogBackdrop, DialogPanel, DialogTitle } from '@headlessui/react';
import Accordion from '@/components/Accordion';
import CourseCardPart from '@/components/CourseCardPart';
import VideoComponent from '@/components/VideoComponent';

export default function VideoCourse({ setContent }) {
  const t = useTranslations('video_course');
  
  const [source, setSource] = useState({
    header: t('videos.the_basics'),
    src: 'https://customer-20uuz446bsq7bzy7.cloudflarestream.com/a199e5dcedf5ac3e00cbceda058328d8/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2Fa199e5dcedf5ac3e00cbceda058328d8%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600',
    week: 1,
  });
  const [open, setOpen] = useState(false);

  return (
    <div>
      <div className="md:flex justify-between pt-10">
        <div className="md:w-[70%]">
          <div
            onClick={() => {
              setContent(1);
            }}
            className="text-[#191919] text-[18px] pb-10 flex font-semibold md:ml-10 cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth="1.5"
              stroke="currentColor"
              class="size-7 pr-2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
            </svg>
            <span className="pt-0.5">{t('back_to_academy')}</span>
          </div>
          <Accordion
            title={t('weeks.week_1')}
            content={
              <div>
                <div className="hidden md:block">
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.the_basics')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/a199e5dcedf5ac3e00cbceda058328d8/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2Fa199e5dcedf5ac3e00cbceda058328d8%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="05:26"
                    week={1}
                  />
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.science_of_neuro_plasticity')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/48f844bc0f480047fda3d0ad2b18648c/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2F48f844bc0f480047fda3d0ad2b18648c%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="06:58"
                    week={1}
                  />
                </div>
                <div
                  className="block md:hidden"
                  onClick={() => {
                    setOpen(true);
                  }}>
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.the_basics')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/a199e5dcedf5ac3e00cbceda058328d8/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2Fa199e5dcedf5ac3e00cbceda058328d8%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="5:26"
                    week={1}
                  />
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.science_of_neuro_plasticity')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/48f844bc0f480047fda3d0ad2b18648c/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2F48f844bc0f480047fda3d0ad2b18648c%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="06:58"
                    week={1}
                  />
                </div>
              </div>
            }
          />
          <Accordion
            title={t('weeks.week_2')}
            content={
              <div>
                <div className="hidden md:block">
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.mastering_memory')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/c667d6330020bf561003edd4581309bd/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2Fc667d6330020bf561003edd4581309bd%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="08:55"
                    week={2}
                  />
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.stress_and_brain')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/18c051a0de0b08ccd5162972bbac83e7/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2F18c051a0de0b08ccd5162972bbac83e7%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="09:10"
                    week={2}
                  />
                </div>
                <div
                  className="block md:hidden"
                  onClick={() => {
                    setOpen(true);
                  }}>
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.mastering_memory')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/c667d6330020bf561003edd4581309bd/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2Fc667d6330020bf561003edd4581309bd%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="08:55"
                    week={2}
                  />
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.stress_and_brain')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/18c051a0de0b08ccd5162972bbac83e7/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2F18c051a0de0b08ccd5162972bbac83e7%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="09:10"
                    week={2}
                  />
                </div>{' '}
              </div>
            }
          />
          <Accordion
            title={t('weeks.week_3')}
            content={
              <div>
                <div className="hidden md:block">
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.brain_exercises')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/6098a95a2545bccb9089e4ab3ede8d78/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2F6098a95a2545bccb9089e4ab3ede8d78%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="08:26"
                    week={3}
                  />
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.science_of_eq')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/da2692c96133eb804d315296b9548b0d/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2Fda2692c96133eb804d315296b9548b0d%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="08:47"
                    week={3}
                  />
                </div>
                <div
                  className="block md:hidden"
                  onClick={() => {
                    setOpen(true);
                  }}>
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.brain_exercises')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/6098a95a2545bccb9089e4ab3ede8d78/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2F6098a95a2545bccb9089e4ab3ede8d78%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="08:26"
                    week={3}
                  />
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.science_of_eq')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/da2692c96133eb804d315296b9548b0d/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2Fda2692c96133eb804d315296b9548b0d%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="08:47"
                    week={3}
                  />
                </div>
              </div>
            }
          />
          <Accordion
            title={t('weeks.week_4')}
            content={
              <div>
                <div className="hidden md:block">
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.cognitive_flexibility')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/f2e170e51dbfb76cd5009c997b1fd5bd/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2Ff2e170e51dbfb76cd5009c997b1fd5bd%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="08:05"
                    week={4}
                  />
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.role_of_creativity')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/76888120f909cab2e3bf43ae50fd3496/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2F76888120f909cab2e3bf43ae50fd3496%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="08:54"
                    week={4}
                  />
                </div>
                <div
                  className="block md:hidden"
                  onClick={() => {
                    setOpen(true);
                  }}>
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.cognitive_flexibility')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/f2e170e51dbfb76cd5009c997b1fd5bd/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2Ff2e170e51dbfb76cd5009c997b1fd5bd%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="08:05"
                    week={4}
                  />
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.role_of_creativity')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/76888120f909cab2e3bf43ae50fd3496/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2F76888120f909cab2e3bf43ae50fd3496%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="08:54"
                    week={4}
                  />
                </div>
              </div>
            }
          />
          <Accordion
            title={t('weeks.week_5')}
            content={
              <div>
                <div className="hidden md:block">
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.mindfulness_clarity')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/0408a0b44508769f5f440d8e3a0e20e3/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2F0408a0b44508769f5f440d8e3a0e20e3%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="09:14"
                    week={5}
                  />
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.power_of_social_interaction')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/5d1e3a05f9fa82ed82a0228844d5d66f/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2F5d1e3a05f9fa82ed82a0228844d5d66f%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="10:21"
                    week={5}
                  />
                </div>{' '}
                <div
                  className="block md:hidden"
                  onClick={() => {
                    setOpen(true);
                  }}>
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.mindfulness_clarity')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/0408a0b44508769f5f440d8e3a0e20e3/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2F0408a0b44508769f5f440d8e3a0e20e3%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="09:14"
                    week={5}
                  />
                  <CourseCardPart
                    setSource={setSource}
                    available={true}
                    header={t('videos.power_of_social_interaction')}
                    src="https://customer-20uuz446bsq7bzy7.cloudflarestream.com/5d1e3a05f9fa82ed82a0228844d5d66f/iframe?poster=https%3A%2F%2Fcustomer-20uuz446bsq7bzy7.cloudflarestream.com%2F5d1e3a05f9fa82ed82a0228844d5d66f%2Fthumbnails%2Fthumbnail.jpg%3Ftime%3D%26height%3D600"
                    time="10:21"
                    week={5}
                  />
                </div>
              </div>
            }
          />
        </div>
        <div className="pt-10 md:pt-0 md:ml-10 mb-30 hidden md:block">
          <p className="text-[#191919] text-[18px] pb-6  md:pb-10 font-semibold">{source.header}</p>
          <VideoComponent source={source} week={source.week} />
        </div>
      </div>
      <div className="py-10 h-10 w-[100%]"></div>
      <Dialog open={open} onClose={setOpen} className="relative z-10">
        <DialogBackdrop
          transition
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
        />

        <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <DialogPanel
              transition
              className="relative m-auto transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full sm:max-w-lg data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95">
              <div className="bg-white px-4 pb-4 pt-5 sm:p-2 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-centersm:mt-0 sm:text-left">
                    <DialogTitle
                      as="h3"
                      className="text-base flex justify-between font-semibold leading-6 text-gray-900">
                      <p className="text-[#191919] text-[18px] font-semibold">{source.header}</p>
                      <div onClick={() => setOpen(false)} className="ml-auto">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          strokeWidth="1.5"
                          stroke="currentColor"
                          class="size-6">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                        </svg>
                      </div>
                    </DialogTitle>
                    <div className="mt-2">
                      <div className=" md:pt-0 mb-30 w-[340px]">
                        <VideoComponent source={source} week={source.week} />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </div>
        </div>
      </Dialog>
    </div>
  );
}
